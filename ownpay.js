// own_pay.js - JavaScript conversion of own_pay.php
const axios = require('axios');
const { ec: EC } = require('elliptic');
const keccak256 = require('keccak256');
const { Web3 }= require('web3');
const BigNumber = require('bignumber.js');

// Import database models
const db = require('../../models');
console.log("Database models loaded:", Object.keys(db));
console.log("UserModel available:", !!db.user);
console.log("RechargeModel available:", !!db.recharge);

const UserModel = db.user;
const RechargeModel = db.recharge;
const WithdrawModel = db.withdraw;
const IncLevelModel = db.inc_level;
const SettingModel = db.setting
const ConfigModel = db.config;
const connection = db.sequelize;

// Global nonce management to prevent conflicts between multiple WalletMonitor instances
const globalNonceCache = new Map();
const globalNonceLocks = new Map();
const globalTransactionLocks = new Map(); // Prevent concurrent transactions from same wallet

console.log("Models initialized:");
console.log("- UserModel:", typeof UserModel);
console.log("- RechargeModel:", typeof RechargeModel);
console.log("- RechargeModel.findAll:", typeof (RechargeModel && RechargeModel.findAll));


class WalletGenerator {
    constructor() {
        this.provider = axios.create({
            baseURL: 'https://bsc-dataseed.binance.org'
        });
    }

    generateWallet() {
        try {
            const ec = new EC('secp256k1');
            // Use a custom random number generator to avoid brorand issues
            const keyPair = ec.genKeyPair({
                entropy: this.generateRandomBytes(32)
            });

            const privateKey = keyPair.getPrivate('hex');
            let publicKey = keyPair.getPublic(false, 'hex');

            publicKey = publicKey.substring(2);
            const address = '0x' + keccak256(Buffer.from(publicKey, 'hex')).toString('hex').substring(24);

            return {
                address: address,
                privateKey: '0x' + privateKey
            };
        } catch (error) {
            console.error('Error generating wallet:', error);
            throw error;
        }
    }

    // Custom random bytes generator to avoid dependency on brorand
    generateRandomBytes(length) {
        const result = Buffer.alloc(length);
        for (let i = 0; i < length; i++) {
            result[i] = Math.floor(Math.random() * 256);
        }
        return result;
    }
}

class WalletMonitor {
    constructor(usdtReceiveWallet, gasWallet, gasPrivateKey) {
        try {
            console.log("WalletMonitor constructor called with:");
            console.log("- USDT Receive Wallet:", usdtReceiveWallet);
            console.log("- Gas Wallet:", gasWallet);
            console.log("- Gas Private Key:", gasPrivateKey ? (gasPrivateKey.substring(0, 6) + "..." + gasPrivateKey.substring(gasPrivateKey.length - 4)) : "undefined");

            this.provider = axios.create({
                baseURL: 'https://bsc-dataseed.binance.org'
            });
            console.log("Provider created with baseURL: https://bsc-dataseed.binance.org");

            this.usdtReceiveWallet = usdtReceiveWallet;
            this.gasWallet = gasWallet;
            this.gasPrivateKey = gasPrivateKey;
            this.usdtContract = '******************************************';
            console.log("USDT Contract set to:", this.usdtContract);

            // Use global nonce management to prevent conflicts between multiple instances
            this.nonceCache = globalNonceCache;
            this.nonceLocks = globalNonceLocks;

            // Validate addresses
            console.log("Validating wallet addresses...");
            if (!/^0x[a-fA-F0-9]{40}$/.test(gasWallet)) {
                console.error("Gas wallet validation failed:", gasWallet);
                throw new Error("Invalid gas wallet address format");
            }
            if (!/^0x[a-fA-F0-9]{40}$/.test(usdtReceiveWallet)) {
                console.error("USDT receive wallet validation failed:", usdtReceiveWallet);
                throw new Error("Invalid USDT receive wallet address format");
            }

            // Validate private key format
            if (!gasPrivateKey || gasPrivateKey.length < 64) {
                console.error("Gas private key validation failed - invalid length");
                throw new Error("Invalid gas private key format");
            }

            // Verify that the private key matches the gas wallet address
            try {
                const web3 = new Web3('https://bsc-dataseed.binance.org');
                let cleanPrivateKey = gasPrivateKey;
                if (!cleanPrivateKey.startsWith('0x')) {
                    cleanPrivateKey = '0x' + cleanPrivateKey;
                }
                const account = web3.eth.accounts.privateKeyToAccount(cleanPrivateKey);
                if (account.address.toLowerCase() !== gasWallet.toLowerCase()) {
                    console.error("Private key does not match gas wallet address");
                    console.error("Expected address:", gasWallet);
                    console.error("Derived address:", account.address);
                    throw new Error("Gas private key does not match gas wallet address");
                }
                console.log("Private key validation successful - matches gas wallet address");
            } catch (keyError) {
                console.error("Error validating private key:", keyError.message);
                throw new Error("Invalid gas private key: " + keyError.message);
            }

            console.log("Wallet addresses validated successfully");
            console.log("WalletMonitor constructor completed successfully");
        } catch (e) {
            console.error("Error in constructor:", e.message);
            console.error("Error stack:", e.stack);
            throw e;
        }
    }

    async getBNBBalance(address) {
        try {
            const response = await this.provider.post('', {
                jsonrpc: '2.0',
                method: 'eth_getBalance',
                params: [address, 'latest'],
                id: 1
            });

            const body = response.data;

            if (!body.result) {
                console.warn(`Warning: Invalid response from node for address ${address}`);
                return 0;
            }

            return parseInt(body.result, 16) / (10 ** 18); // Convert Wei to BNB
        } catch (e) {
            console.error("Error fetching BNB balance:", e.message);
            return 0;
        }
    }

    async getUSDTBalance(address) {
        try {
            // Create function signature for balanceOf(address)
            const web3 = new Web3('https://bsc-dataseed.binance.org');
            const methodID = web3.utils.sha3('balanceOf(address)').substring(0, 10);
            const params = address.substring(2).padStart(64, '0');
            const data = methodID + params;

            const response = await this.provider.post('', {
                jsonrpc: '2.0',
                method: 'eth_call',
                params: [{
                    to: this.usdtContract,
                    data: data
                }, 'latest'],
                id: 1
            });

            const body = response.data;
            return parseInt(body.result, 16) / (10 ** 18); // Convert to USDT (18 decimals)
        } catch (e) {
            console.error("Error fetching USDT balance:", e.message);
            return 0;
        }
    }

    async monitorAndTransfer(wallet) {
        try {
            console.log("=== Starting monitorAndTransfer process ===");
            console.log("Checking wallet:", wallet.address);
            console.log("Wallet private key present:", !!wallet.privateKey);
            console.log("Wallet private key type:", typeof wallet.privateKey);
            console.log("Wallet private key length:", wallet.privateKey ? wallet.privateKey.length : 'N/A');

            // Validate wallet object
            if (!wallet || !wallet.address || !wallet.privateKey) {
                throw new Error("Invalid wallet object - missing address or private key");
            }

            // Validate wallet address format
            if (!/^0x[a-fA-F0-9]{40}$/.test(wallet.address)) {
                throw new Error(`Invalid wallet address format: ${wallet.address}`);
            }

            // Validate private key format
            let cleanPrivateKey = String(wallet.privateKey).replace('0x', '');
            if (cleanPrivateKey.length !== 64 || !/^[a-fA-F0-9]{64}$/.test(cleanPrivateKey)) {
                throw new Error(`Invalid wallet private key format. Length: ${cleanPrivateKey.length}, Expected: 64 hex characters`);
            }

            console.log("✅ Wallet validation passed");

            console.log("Fetching initial balances...");
            // Get initial balances
            const bnbBalance = await this.getBNBBalance(wallet.address);
            const usdtBalance = await this.getUSDTBalance(wallet.address);
            console.log("Balance fetch completed");

            console.log("BNB Balance:", bnbBalance, "BNB");
            console.log("USDT Balance:", usdtBalance, "USDT");

            // Define minimum thresholds
            const MIN_USDT_THRESHOLD = 0.00001; // Minimum USDT amount worth processing
            const MIN_BNB_REQUIRED = 0.005; // Minimum BNB needed for gas

            // First check if USDT balance is worth processing
            if (usdtBalance < MIN_USDT_THRESHOLD) {
                console.log(`USDT balance too small to process (< ${MIN_USDT_THRESHOLD})`);
                console.log("=== monitorAndTransfer process completed (no significant balance) ===");
                return {
                    found: false,
                    message: 'No significant USDT balance found'
                };
            }

            // If USDT balance is significant, ensure we have enough BNB
            if (bnbBalance < MIN_BNB_REQUIRED) {
                console.log("Insufficient BNB for gas. Attempting to send from main wallet...");

                // Try to send BNB from gas wallet
                if (!await this.sendGasFromMainWallet(wallet.address)) {
                    console.log("Failed to send BNB for gas");
                    console.log("=== monitorAndTransfer process completed (failed to send gas) ===");
                    return {
                        found: false,
                        message: 'Failed to send BNB for gas'
                    };
                }

                // Wait for BNB transfer to confirm
                await new Promise(resolve => setTimeout(resolve, 15000));

                // Verify BNB was received
                const newBnbBalance = await this.getBNBBalance(wallet.address);
                if (newBnbBalance < MIN_BNB_REQUIRED) {
                    console.log("BNB transfer failed to arrive");
                    console.log("=== monitorAndTransfer process completed (BNB transfer failed to arrive) ===");
                    return {
                        found: false,
                        message: 'BNB transfer failed to arrive'
                    };
                }
            }

            // Now proceed with USDT transfer
            console.log("Proceeding with USDT transfer...");

            try {
                // The transferUSDT function now handles both blockchain transaction and database update
                // It only returns true if both were successful
                const success = await this.transferUSDT(wallet.address, wallet.privateKey, usdtBalance);

                if (!success) {
                    console.log("USDT transfer failed");
                    // Return remaining BNB to gas wallet
                    try {
                        const finalBnbBalance = await this.getBNBBalance(wallet.address);
                        console.log(`Final BNB balance to return: ${finalBnbBalance} BNB`);
                        if (finalBnbBalance > 0.001) {
                            console.log("Attempting to return remaining BNB to gas wallet...");
                            console.log(`Wallet address: ${wallet.address}`);
                            console.log(`Private key present: ${!!wallet.privateKey}`);
                            console.log(`Private key type: ${typeof wallet.privateKey}`);

                            await this.transferBNB(wallet.address, wallet.privateKey, finalBnbBalance);
                            console.log("Returned remaining BNB to gas wallet after failed USDT transfer");
                        } else {
                            console.log("BNB balance too small to return");
                        }
                    } catch (bnbError) {
                        console.error("Error returning BNB after failed USDT transfer:", bnbError.message);
                        console.error("BNB Error stack:", bnbError.stack);
                    }
                    console.log("=== monitorAndTransfer process completed (USDT transfer failed) ===");
                    return {
                        found: false,
                        message: 'USDT transfer failed. Blockchain transaction or database update unsuccessful.'
                    };
                }

                // If we get here, both blockchain transaction and database update were successful

                // Return remaining BNB to gas wallet
                try {
                    const finalBnbBalance = await this.getBNBBalance(wallet.address);
                    console.log(`Final BNB balance to return: ${finalBnbBalance} BNB`);
                    if (finalBnbBalance > 0.001) {
                        console.log("Attempting to return remaining BNB to gas wallet...");
                        console.log(`Wallet address: ${wallet.address}`);
                        console.log(`Private key present: ${!!wallet.privateKey}`);
                        console.log(`Private key type: ${typeof wallet.privateKey}`);

                        await this.transferBNB(wallet.address, wallet.privateKey, finalBnbBalance);
                        console.log("Returned remaining BNB to gas wallet after successful USDT transfer");
                    } else {
                        console.log("BNB balance too small to return");
                    }
                } catch (bnbError) {
                    console.error("Error returning BNB after successful USDT transfer:", bnbError.message);
                    console.error("BNB Error stack:", bnbError.stack);
                    // Continue with success response even if BNB return fails
                }

                console.log("=== monitorAndTransfer process completed successfully ===");
                return {
                    found: true,
                    amount: usdtBalance,
                    currency: 'USDT',
                    message: 'Transfer completed successfully'
                };

            } catch (transferError) {
                console.error("Error in USDT transfer process:", transferError.message);
                console.log("=== monitorAndTransfer process completed (error in USDT transfer) ===");
                return {
                    found: false,
                    message: 'Error in USDT transfer: ' + transferError.message
                };
            }

        } catch (e) {
            console.error("Error in monitoring:", e.message);
            console.log("=== monitorAndTransfer process failed with error ===");
            return {
                found: false,
                message: 'Error: ' + e.message
            };
        }
    }

    async approveUSDT(fromAddress, privateKey, amount) {
        try {
            console.log("Approving USDT transfer...");

            privateKey = privateKey.replace('0x', '');

            // Create approve function data
            const web3 = new Web3('https://bsc-dataseed.binance.org');
            const methodID = web3.utils.sha3('approve(address,uint256)').substring(0, 10);
            const spender = this.usdtReceiveWallet.substring(2).padStart(64, '0');

            // Convert amount to wei format
            const bn = new BigNumber(amount).times(new BigNumber(10).pow(18));
            const amountHex = bn.toString(16).padStart(64, '0');

            const data = methodID + spender + amountHex;

            const nonce = await this.getNextNonce(fromAddress);
            console.log(`Using nonce for USDT approval: ${nonce}`);

            const txParams = {
                nonce: '0x' + nonce.toString(16),
                to: this.usdtContract,
                value: '0x0',
                data: data,
                gas: '0x186A0', // 100000 gas limit
                gasPrice: '0x' + (5 * 10 ** 9).toString(16), // 5 Gwei
                chainId: 56
            };

            const web3Instance = new Web3('https://bsc-dataseed.binance.org');
            const account = web3Instance.eth.accounts.privateKeyToAccount(privateKey);
            const signedTx = await account.signTransaction(txParams);

            const txHash = await this.sendRawTransaction(signedTx.rawTransaction);

            // Wait for approval confirmation
            for (let i = 0; i < 30; i++) {
                const status = await this.getTransactionStatus(txHash);
                if (status === true) {
                    console.log("Approval confirmed");
                    await new Promise(resolve => setTimeout(resolve, 5000)); // Wait a bit for blockchain to update
                    return true;
                } else if (status === false) {
                    console.log("Approval transaction failed");
                    return false;
                }
                await new Promise(resolve => setTimeout(resolve, 5000));
            }

            console.log("Approval transaction timeout");
            return false;
        } catch (e) {
            console.error("Error in approveUSDT:", e.message);
            return false;
        }
    }

    // async transferUSDT(fromAddress, privateKey, amount) {
    //     try {
    //         const actualBalance = await this.getUSDTBalance(fromAddress);
    //         amount = Math.min(amount, actualBalance);
    //         amount = Math.round(amount * 1000000) / 1000000; // Round to 6 decimal places

    //         console.log(`Attempting to transfer ${amount} USDT from ${fromAddress} to ${this.usdtReceiveWallet}`);

    //         // Direct transfer without allowance

    //         // Create a web3 instance
    //         const web3 = new Web3('https://bsc-dataseed.binance.org');

    //         // Create contract ABI for the transfer function
    //         const transferAbi = {
    //             name: 'transfer',
    //             type: 'function',
    //             inputs: [
    //                 { type: 'address', name: 'recipient' },
    //                 { type: 'uint256', name: 'amount' }
    //             ]
    //         };

    //         // Encode the function call properly using web3
    //         let transactionData;
    //         try {
    //             transactionData = web3.eth.abi.encodeFunctionCall(transferAbi, [
    //                 this.usdtReceiveWallet,
    //                 web3.utils.toWei(amount.toString(), 'ether')
    //             ]);

    //             console.log(`Using recipient address: ${this.usdtReceiveWallet}`);
    //             console.log(`Using amount wei: ${web3.utils.toWei(amount.toString(), 'ether')}`);
    //             console.log(`Encoded transaction data: ${transactionData}`);

    //             // Verify data is valid hex
    //             if (!/^0x[0-9a-f]+$/i.test(transactionData)) {
    //                 console.error("Invalid hex data format:", transactionData);
    //                 return false;
    //             }
    //         } catch (error) {
    //             console.error("Error encoding transaction data:", error.message);
    //             return false;
    //         }

    //         const txParams = {
    //             nonce: '0x' + (await this.getTransactionCount(fromAddress)).toString(16),
    //             to: this.usdtContract,
    //             value: '0x0',
    //             data: transactionData,
    //             gas: '0x186A0', // 100000 gas
    //             gasPrice: '0x' + (3 * 10 ** 9).toString(16), // 3 Gwei
    //             chainId: 56
    //         };

    //         // First, attempt the blockchain transaction
    //         let txSuccess = false;
    //         let txHash = '';

    //         try {
    //             const web3Instance = new Web3('https://bsc-dataseed.binance.org');
    //             const cleanPrivateKey = privateKey.replace('0x', '');
    //             const account = web3Instance.eth.accounts.privateKeyToAccount(cleanPrivateKey);

    //             console.log("Signing transaction...");
    //             const signedTx = await account.signTransaction(txParams);

    //             console.log("Sending transaction...");
    //             txHash = await this.sendRawTransaction2(signedTx.rawTransaction);
    //             console.log(`Transaction sent with hash: ${txHash}`);

    //             // Wait for transaction confirmation
    //             await new Promise(resolve => setTimeout(resolve, 8000));

    //             // Verify the transfer was successful by checking the new balance
    //             const newBalance = await this.getUSDTBalance(fromAddress);
    //             console.log(`New USDT balance after transfer: ${newBalance}`);

    //             // Check if the balance is significantly reduced
    //             const significantReduction = newBalance < (amount * 0.1);
    //             txSuccess = newBalance < 0.0001 || significantReduction;

    //             if (!txSuccess) {
    //                 console.log("Blockchain transaction verification failed - balance not reduced enough");
    //                 return false;
    //             }

    //             console.log("Blockchain transaction successful, proceeding to update database");
    //         } catch (txError) {
    //             console.error("Blockchain transaction error:", txError.message);
    //             return false;
    //         }

    //         // Only update the database if the blockchain transaction was successful
    //         if (txSuccess) {
    //             try {
    //                 const [result] = await connection.query('SELECT * FROM users WHERE pay_address = ? LIMIT 1', [fromAddress]);
    //                 const row = result[0];

    //                 if (!row) {
    //                     console.error("User not found in database");
    //                     return false;
    //                 }

    //                 const [usdt_bonus] = await connection.query("SELECT `value` FROM `tbl_config` WHERE `name` = 'usdt_bonus'");
    //                 const usdtBonus = parseFloat(usdt_bonus[0] && usdt_bonus[0].value || '0');

    //                 // Generate a transaction ID using timestamp and txHash
    //                 const txid = `USDT_${txHash.substring(0, 10)}_${Date.now()}`;

    //                 // Convert USDT to INR at rate of 90
    //                 let money_inr = Math.floor(amount * 90);
    //                 let money_inrtotal = money_inr;
    //                 money_inr += (money_inr * usdtBonus) / 100;

    //                 console.log("USDT amount in INR with bonus:", money_inr);

    //                 // Insert into recharge table
    //                 const sql = `INSERT INTO recharge (id_order, transaction_id, utr, phone, money, type, status, today, url, time)
    //                             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    //                 await connection.query(sql, [
    //                     row.id_user,
    //                     txid,
    //                     txHash,  // Use actual transaction hash as UTR
    //                     row.phone,
    //                     money_inrtotal,
    //                     'USDT',
    //                     1, // Set as completed
    //                     new Date().toISOString().split('T')[0],
    //                     'AUTO',
    //                     Date.now()
    //                 ]);

    //                 // Update user's wallet
    //                 await connection.query(
    //                     'UPDATE users SET got = 1, temp_money = temp_money + ?, money = money + ?, total_money = total_money + ? WHERE pay_address = ?',
    //                     [money_inr, money_inr, money_inr, fromAddress]
    //                 );

    //                 // Fetch recharge level config for referral bonuses
    //                 const [levelConfigs] = await connection.query("SELECT name, value FROM tbl_config WHERE name LIKE 'recharge_level_%'");
    //                 const levelPercents = {};
    //                 levelConfigs.forEach(cfg => {
    //                     levelPercents[cfg.name] = parseFloat(cfg.value);
    //                 });

    //                 let currentInviteCode = row.invite;
    //                 let currentLevel = 1;

    //                 // Process referral bonuses
    //                 while (currentInviteCode && currentLevel <= 6) {
    //                     const [uplineData] = await connection.query(`SELECT phone, invite FROM users WHERE code = ?`, [currentInviteCode]);

    //                     if (uplineData.length === 0) break;

    //                     const uplinePhone = uplineData[0].phone;
    //                     const uplineInvite = uplineData[0].invite;

    //                     const levelKey = `recharge_level_${currentLevel}`;
    //                     const percent = levelPercents[levelKey] || 0;

    //                     if (percent > 0) {
    //                         const levelIncome = (money_inrtotal * percent) / 100;

    //                         await connection.query(
    //                             `INSERT INTO inc_level (level, phone, from_id, percent, amount, date_time)
    //                             VALUES (?, ?, ?, ?, ?, ?)`,
    //                             [currentLevel, uplinePhone, row.phone, percent, levelIncome, new Date()]
    //                         );

    //                         await connection.query(
    //                             `UPDATE users SET money = money + ?, total_money = total_money + ? WHERE phone = ?`,
    //                             [levelIncome, levelIncome, uplinePhone]
    //                         );
    //                     }

    //                     // Move to next level
    //                     currentInviteCode = uplineInvite;
    //                     currentLevel++;
    //                 }

    //                 // Verify the database update was successful
    //                 const [userAfterUpdate] = await connection.query('SELECT money, temp_money, total_money FROM users WHERE pay_address = ?', [fromAddress]);
    //                 if (userAfterUpdate && userAfterUpdate[0]) {
    //                     console.log(`User balance after update: Money=${userAfterUpdate[0].money}, Temp=${userAfterUpdate[0].temp_money}, Total=${userAfterUpdate[0].total_money}`);
    //                 }

    //                 console.log(`Successfully updated database for user ${row.phone} with ${money_inr} INR (${amount} USDT)`);
    //                 return true;

    //             } catch (dbError) {
    //                 console.error("Database error in transferUSDT:", dbError.message);
    //                 // The blockchain transaction was successful but database update failed
    //                 // This is a critical error that needs manual intervention
    //                 console.error("CRITICAL ERROR: Blockchain transaction successful but database update failed. Manual reconciliation required.");
    //                 return false;
    //             }
    //         }

    //         return false; // Should not reach here, but just in case
    //     } catch (e) {
    //         console.error("Error in transferUSDT:", e.message);
    //         return false;
    //     }
    // }

     async transferUSDT(fromAddress, privateKey, amount) {
        try {
            const actualBalance = await this.getUSDTBalance(fromAddress);
            amount = Math.min(amount, actualBalance);
            amount = Math.round(amount * 1000000) / 1000000; // Round to 6 decimal places

            console.log(`Attempting to transfer ${amount} USDT from ${fromAddress} to ${this.usdtReceiveWallet}`);

            // Create a web3 instance
            const web3 = new Web3(new Web3.providers.HttpProvider('https://bsc-dataseed.binance.org'));

            // Create contract ABI for the transfer function
            const transferAbi = {
                name: 'transfer',
                type: 'function',
                inputs: [
                    { type: 'address', name: 'recipient' },
                    { type: 'uint256', name: 'amount' }
                ]
            };

            // Encode the function call properly using web3
            let transactionData;
            try {
                transactionData = web3.eth.abi.encodeFunctionCall(transferAbi, [
                    this.usdtReceiveWallet,
                    web3.utils.toWei(amount.toString(), 'ether')
                ]);

                console.log(`Using recipient address: ${this.usdtReceiveWallet}`);
                console.log(`Using amount wei: ${web3.utils.toWei(amount.toString(), 'ether')}`);
                console.log(`Encoded transaction data: ${transactionData}`);

                // Verify data is valid hex
                if (!/^0x[0-9a-f]+$/i.test(transactionData)) {
                    console.error("Invalid hex data format:", transactionData);
                    return false;
                }
            } catch (error) {
                console.error("Error encoding transaction data:", error.message);
                return false;
            }

            const nonce = await this.getNextNonce(fromAddress);
            console.log(`Using nonce for USDT transfer: ${nonce}`);

            const txParams = {
                nonce: '0x' + nonce.toString(16),
                to: this.usdtContract,
                value: '0x0',
                data: transactionData,
                gas: '0x186A0', // 100000 gas
                gasPrice: '0x' + (3 * 10 ** 9).toString(16), // 3 Gwei
                chainId: 56
            };

            // Log transaction details
            console.log(`Processing transfer of ${amount} USDT from ${fromAddress} to ${this.usdtReceiveWallet}`);

            // Sign and send the transaction
            try {
                const web3Instance = new Web3(new Web3.providers.HttpProvider('https://bsc-dataseed.binance.org'));
                const cleanPrivateKey = privateKey.replace('0x', '');
                const account = web3Instance.eth.accounts.privateKeyToAccount('0x' + cleanPrivateKey);

                console.log("Signing transaction...");
                const signedTx = await account.signTransaction(txParams);

                console.log("Sending transaction...");

                // First send the transaction to the blockchain
                const txHash = await this.sendRawTransaction2(signedTx.rawTransaction);
                console.log(`Transaction sent with hash: ${txHash}`);

                // Wait for transaction confirmation
                await new Promise(resolve => setTimeout(resolve, 8000));

                // Verify the transfer was successful by checking the new balance
                const newBalance = await this.getUSDTBalance(fromAddress);
                console.log(`New USDT balance after transfer: ${newBalance}`);

                // Only proceed with database operations if transaction was successful
                if (newBalance < 0.0001) {
                    try {
                        // Find user by wallet address
                        console.log("Looking for user with wallet address:", fromAddress);
                        const user = await UserModel.findOne({ where: { pay_address: fromAddress } });

                        console.log("User found:", user ? `ID: ${user.id}, Mobile: ${user.mobile}` : "null");

                        if (!user) {
                            console.error("User not found with wallet address:", fromAddress);
                            return true; // Transaction was successful even if we couldn't update the database
                        }

                        // // Get USDT bonus rate from config if available
                        // const [usdt_bonus] = await connection.query("SELECT `value` FROM `tbl_config` WHERE `name` = 'usdt_bonus'");
                        // const usdtBonus = parseFloat(usdt_bonus[0] && usdt_bonus[0].value || '0');
                        var usdtBonus = 0;
                        // Generate a transaction ID
                        const txid = `USDT_${txHash.substring(0, 10)}_${Date.now()}`;

                        // Convert USDT to INR at rate of 90 (or your preferred rate)
                        const conversionRate = 90;
                        let money_inr = Math.floor(amount * conversionRate);
                        let money_inrtotal = money_inr;
                        // Count deposits from recharge table
                        console.log("Counting previous deposits for user ID:", user.id);
                        console.log("RechargeModel available:", !!RechargeModel);
                        console.log("RechargeModel type:", typeof RechargeModel);

                        if (!RechargeModel || typeof RechargeModel.findAll !== 'function') {
                            console.error("RechargeModel is not properly imported or initialized");
                            throw new Error("RechargeModel is not available");
                        }

                        const rechargeRecords = await RechargeModel.findAll({
                            where: { userId: user.id }
                        });
                        const countOfDeposit = rechargeRecords.length;

                        console.log("Previous deposit count:", countOfDeposit);
                        console.log("Recharge records found:", rechargeRecords.map(r => ({ id: r.id, money: r.money, type: r.type })));
                        // Apply bonus if configured
                        if(countOfDeposit<1){
                            
                            if (money_inrtotal >= 100 && money_inrtotal <= 299) {
                                usdtBonus = 20;
                            } else if (money_inrtotal >= 300 && money_inrtotal <= 999) {
                                usdtBonus = 60;
                            } else if (money_inrtotal >= 1000 && money_inrtotal <= 1999) {
                                usdtBonus = 200;
                            } else if (money_inrtotal >= 2000 && money_inrtotal <= 4999) {
                                usdtBonus = 400;
                            } else if (money_inrtotal >= 5000 && money_inrtotal <= 19999) {
                                usdtBonus = 800;
                            } else if (money_inrtotal >= 20000 && money_inrtotal <= 49999) {
                                usdtBonus = 1000;
                            } else if (money_inrtotal >= 50000 && money_inrtotal <= 99999) {
                                usdtBonus = 1500;
                            } else if (money_inrtotal >= 100000 && money_inrtotal <= 200000) {
                                usdtBonus = 3000;
                            }
                            
                        } else if(countOfDeposit==1){
                            //  Second Deposite condition need to apply
                            if (money_inrtotal >= 20000 && money_inrtotal <= 49999) {
                                usdtBonus = 1000;
                            } else if (money_inrtotal >= 50000 && money_inrtotal <= 99999) {
                                usdtBonus = 1500;
                            } else if (money_inrtotal >= 100000 && money_inrtotal <= 199999) {
                                usdtBonus = 3000;
                            }
                            
                        }else{
                            //  daily recharge bonus need to apply
                            
                            if (money_inrtotal >= 1000 && money_inrtotal < 5000) {
                                usdtBonus = 41;
                            } else if (money_inrtotal >= 5000 && money_inrtotal < 10000) {
                                usdtBonus = 101;
                            } else if (money_inrtotal >= 10000 && money_inrtotal < 50000) {
                                usdtBonus = 161;
                            } else if (money_inrtotal >= 50000) {
                                usdtBonus = 301;
                            }
                            
                            
                        }
                        
                        
                        money_inrtotal +=  usdtBonus;

                        console.log("USDT amount in INR :", money_inrtotal);

                        // Insert into recharge table
                        console.log("Creating recharge record...");
                        console.log("Recharge data:", {
                            userId: user.id,
                            transaction_id: txid,
                            utr: txHash,
                            phone: user.mobile,
                            money: money_inrtotal,
                            type: 'USDT',
                            status: 1
                        });

                        const currentTime = new Date().toISOString();

                        const rechargeRecord = await RechargeModel.create({
                            userId: user.id,
                            transaction_id: txid,
                            utr: txHash, // Use actual transaction hash as UTR
                            phone: user.mobile,
                            money: money_inrtotal,
                            type: 'USDT',
                            status: 1, // Set as completed
                            time:  Date.now() // Required field
                        });

                        console.log("Recharge record created successfully with ID:", rechargeRecord.id);

                        // Update user's wallet
                        console.log("Updating user wallet...");
                        console.log(`Current wallet: ${user.wallet}, Adding: ${money_inrtotal} (includes bonus)`);
                        console.log(`Current todays_recharge: ${user.todays_recharge}, Adding: ${money_inrtotal}`);

                        const updateResult = await UserModel.update(
                            {
                                wallet: user.wallet + money_inrtotal,
                                todays_recharge: user.todays_recharge + money_inrtotal
                            },
                            { where: { id: user.id } }
                        );

                        console.log("User wallet update result:", updateResult);
                        console.log("User wallet updated successfully");
                      

                        console.log(`Successfully updated database for user ${user.mobile} with ${money_inrtotal} INR (${amount} USDT, includes bonus)`);

                        // Process referral bonuses if applicable
                        // Referral bonus is commented 
//                         try {
//                             // Fetch recharge level config for referral bonuses
//                             const levelSettings = await SettingModel.findAll({
//                                 where : {
//                                     name : {
//                                         [Op.like] : 'level_%'
//                                     }
//                                 }
//                             });
//                             // chage this to setting modal and fetch all level from tbl_setting
                            
                            
                            
//                             // const [levelConfigs] = await connection.query("SELECT name, value FROM tbl_config WHERE name LIKE 'recharge_level_%'");
//                             // const levelPercents = {};
//                             // levelConfigs.forEach(cfg => {
//                             //     levelPercents[cfg.name] = parseFloat(cfg.value);
//                             // });
//                             levelSettings.forEach(setting => {
//                                 levelPercents[setting.name] = parseFloat(setting.value);
//                             });

//                             let currentInviteCode = user.invite;
//                             let currentLevel = 1;

//                             // Process referral bonuses
//                             while (currentInviteCode && currentLevel <= 6) {
//                                 //  get upline user of that user via refer id or anything that exist here for sponser tracking
//                                 const uplineUser = await UserModel.findOne({
//                                     where : {referred_by : currentInviteCode }
//                                 })
                                
//                                 // const [uplineData] = await connection.query(`SELECT phone, invite FROM users WHERE code = ?`, [currentInviteCode]);

//                                 if (uplineData.length === 0) break;

//                                 const uplinePhone = uplineData[0].phone;
//                                 const uplineInvite = uplineData[0].invite;

//                                 const levelKey = `level_${currentLevel}`;
//                                 const percent = levelPercents[levelKey] || 0;

//                                 if (percent > 0) {
//                                     const levelIncome = (money_inrtotal * percent) / 100;
// //  search level recharge history table and upodate user upline wallet
//                                     // await connection.query(
//                                     //     `INSERT INTO inc_level (level, phone, from_id, percent, amount, date_time)
//                                     //     VALUES (?, ?, ?, ?, ?, ?)`,
//                                     //     [currentLevel, uplinePhone, user.phone, percent, levelIncome, new Date()]
//                                     // );

//                                     // await connection.query(
//                                     //     `UPDATE users SET money = money + ?, total_money = total_money + ? WHERE phone = ?`,
//                                     //     [levelIncome, levelIncome, uplinePhone]
//                                     // );
//                                 }

//                                 // Move to next level
//                                 currentInviteCode = uplineInvite;
//                                 currentLevel++;
//                             }
//                         } catch (referralError) {
//                             console.error("Error processing referral bonuses:", referralError.message);
//                             // Continue with success since the main transaction was successful
//                         }

                        return true;
                    } catch (dbError) {
                        console.error("Database error in transferUSDT:", dbError.message);
                        // The blockchain transaction was successful but database update failed
                        console.error("CRITICAL ERROR: Blockchain transaction successful but database update failed. Manual reconciliation required.");
                        return true; // Still return true since the blockchain transaction was successful
                    }
                } else {
                    console.log("Transaction may have failed - balance is still significant");
                    return false;
                }
            } catch (txError) {
                console.error("Transaction error:", txError.message);
                return false;
            }
        } catch (e) {
            console.error("Error in transferUSDT:", e.message);
            return false;
        }
    }


    async transferBNB(fromAddress, privateKey, amount, retryCount = 0) {
        const maxRetries = 3;

        try {
            // Convert amount to Wei for precise calculations
            const amountInWei = amount * (10 ** 18);

            // Gas limit in Wei (21000 gas for simple transfer)
            const gasLimit = 21000;

            // Gas price in Wei (3 Gwei)
            const gasPriceInWei = 3 * (10 ** 9);

            // Calculate total gas cost in Wei
            const gasCostInWei = gasLimit * gasPriceInWei;

            // Check if we have enough for gas + transfer
            if (amountInWei <= gasCostInWei) {
                console.log("Amount too small to transfer after gas costs");
                return false;
            }

            // Calculate amount to send (total - gas cost)
            const sendAmountInWei = amountInWei - gasCostInWei;
            const sendAmount = sendAmountInWei / (10 ** 18);

            console.log(`Transferring ${sendAmount} BNB to gas wallet: ${this.gasWallet}`);
            console.log(`Gas cost: ${gasCostInWei / (10 ** 18)} BNB`);

            // Validate and clean private key
            console.log("=== transferBNB Debug Info ===");
            console.log(`Private key present: ${!!privateKey}`);
            console.log(`Private key type: ${typeof privateKey}`);
            console.log(`Private key length: ${privateKey ? privateKey.length : 'N/A'}`);

            if (!privateKey) {
                throw new Error("Private key is undefined or null");
            }

            // Ensure private key is a string
            privateKey = String(privateKey);

            // Remove '0x' if present from private key
            privateKey = privateKey.replace('0x', '');

            // Validate private key format
            if (privateKey.length !== 64) {
                throw new Error(`Invalid private key length: ${privateKey.length}. Expected: 64 characters`);
            }

            if (!/^[a-fA-F0-9]{64}$/.test(privateKey)) {
                throw new Error("Invalid private key format. Must be 64 hex characters");
            }

            console.log("Private key validation passed");

            const nonce = await this.getNextNonce(fromAddress);
            console.log(`Using nonce: ${nonce}`);

            const txParams = {
                nonce: '0x' + nonce.toString(16),
                to: this.gasWallet,
                value: '0x' + Math.floor(sendAmountInWei).toString(16),
                gas: '0x' + gasLimit.toString(16),
                gasPrice: '0x' + gasPriceInWei.toString(16),
                chainId: 56
            };

            console.log("Transaction parameters:", txParams);

            const web3Instance = new Web3('https://bsc-dataseed.binance.org');
            console.log("Creating account from private key...");
            const account = web3Instance.eth.accounts.privateKeyToAccount('0x' + privateKey);
            console.log(`Account created: ${account.address}`);

            console.log("Signing transaction...");
            const signedTx = await account.signTransaction(txParams);
            console.log("Transaction signed successfully");

            const txHash = await this.sendRawTransaction(signedTx.rawTransaction);

            console.log("Transaction sent successfully! TxHash:", txHash);

            // Wait for confirmation
            const maxAttempts = 30;
            for (let i = 0; i < maxAttempts; i++) {
                const receipt = await this.getDetailedTransactionReceipt(txHash);

                if (receipt === null) {
                    console.log(`Waiting for transaction confirmation... Attempt ${i + 1}/${maxAttempts}`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    continue;
                }

                if (receipt.status === '0x1') {
                    console.log("BNB transfer confirmed");
                    return true;
                } else {
                    console.log("BNB transfer failed");
                    return false;
                }
            }

            console.log("Transaction confirmation timeout");
            return false;
        } catch (e) {
            console.error("Error in transferBNB:", e.message);

            // Handle nonce errors with retry
            const shouldRetry = await this.handleNonceError(fromAddress, e, retryCount);
            if (shouldRetry && retryCount < maxRetries) {
                console.log(`Retrying transferBNB due to nonce error (attempt ${retryCount + 2}/${maxRetries + 1})`);
                return await this.transferBNB(fromAddress, privateKey, amount, retryCount + 1);
            }

            return false;
        }
    }

    async getTransactionCount(address) {
        const response = await this.provider.post('', {
            jsonrpc: '2.0',
            method: 'eth_getTransactionCount',
            params: [address, 'latest'],
            id: 1
        });
        const body = response.data;
        return parseInt(body.result, 16);
    }

    async getNextNonce(address, retryCount = 0) {
        const maxRetries = 3;

        // Ensure we don't have concurrent nonce requests for the same address
        const lockKey = `lock_${address}`;

        // Wait for any existing lock to be released
        while (this.nonceLocks.has(lockKey)) {
            await new Promise(resolve => setTimeout(resolve, 10));
        }

        // Set lock
        this.nonceLocks.set(lockKey, true);

        try {
            // Get current nonce from blockchain with retry logic
            let currentNonce;
            try {
                currentNonce = await this.getTransactionCount(address);
            } catch (error) {
                console.error(`Error getting transaction count for ${address}:`, error.message);
                if (retryCount < maxRetries) {
                    console.log(`Retrying getTransactionCount (attempt ${retryCount + 2}/${maxRetries + 1})`);
                    await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
                    return await this.getNextNonce(address, retryCount + 1);
                }
                throw error;
            }

            // Check if we have a cached nonce for this address
            const cachedNonce = this.nonceCache.get(address) || 0;

            // Use the higher of the two (blockchain nonce or cached nonce)
            const nextNonce = Math.max(currentNonce, cachedNonce);

            // Update cache with next nonce
            this.nonceCache.set(address, nextNonce + 1);

            console.log(`Nonce management for ${address}: blockchain=${currentNonce}, cached=${cachedNonce}, using=${nextNonce}`);

            return nextNonce;
        } finally {
            // Release lock
            this.nonceLocks.delete(lockKey);
        }
    }

    // Method to reset nonce cache for an address (useful when transactions fail)
    resetNonceCache(address) {
        console.log(`Resetting nonce cache for address: ${address}`);
        this.nonceCache.delete(address);
    }

    // Get fresh nonce right before transaction sending (bypasses cache for critical operations)
    async getFreshNonce(address) {
        try {
            console.log(`Getting fresh nonce for ${address}...`);
            const currentNonce = await this.getTransactionCount(address);
            console.log(`Fresh nonce from blockchain: ${currentNonce}`);

            // Update cache to prevent conflicts
            this.nonceCache.set(address, currentNonce + 1);

            return currentNonce;
        } catch (error) {
            console.error(`Error getting fresh nonce for ${address}:`, error.message);
            // Fallback to cached nonce management
            return await this.getNextNonce(address);
        }
    }

    // Method to handle nonce errors and retry
    async handleNonceError(address, error, retryAttempt = 0) {
        const isNonceError = error.message.includes('nonce too low') ||
                           error.message.includes('nonce too high') ||
                           error.message.includes('replacement transaction underpriced') ||
                           error.message.includes('already known');

        if (isNonceError) {
            console.log(`Nonce error detected for ${address}: ${error.message}`);
            this.resetNonceCache(address);

            // Exponential backoff: wait longer for each retry attempt
            const waitTime = Math.min(2000 * Math.pow(2, retryAttempt), 10000); // Max 10 seconds
            console.log(`Waiting ${waitTime}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));

            return true; // Indicates that retry is recommended
        }
        return false; // No nonce error, don't retry
    }

    async sendRawTransaction(signedTx) {
        try {
            const response = await this.provider.post('', {
                jsonrpc: '2.0',
                method: 'eth_sendRawTransaction',
                params: [signedTx],
                id: 1
            });

            const body = response.data;

            if (body.error) {
                console.error("Blockchain error details:", body.error);
                throw new Error("Error sending transaction: " + body.error.message);
            }

            console.log("Transaction sent successfully! TxHash:", body.result);
            return body.result;
        } catch (error) {
            console.error("Network error in sendRawTransaction:", error.message);
            throw error;
        }
    }

    async sendRawTransaction2(signedTx) {
        try {
            const response = await this.provider.post('', {
                jsonrpc: '2.0',
                method: 'eth_sendRawTransaction',
                params: [signedTx],
                id: 1
            });

            const body = response.data;

            if (body.error) {
                console.log("Error sending transaction:", body.error.message);
                throw new Error(body.error.message);
            } else {
                console.log("Transaction sent successfully! TxHash:", body.result);

                // Wait a bit for the transaction to propagate
                await new Promise(resolve => setTimeout(resolve, 2000));

                return body.result;
            }
        } catch (e) {
            console.error("Error in sendRawTransaction2:", e.message);
            throw e; // Re-throw to properly handle in the calling function
        }
    }

    async transferBNBFromGasWallet(toAddress, amount, retryCount = 0) {
        const maxRetries = 3;
        const transactionKey = `${this.gasWallet}_${toAddress}`;

        // Check if there's already a transaction in progress for this wallet pair
        if (globalTransactionLocks.has(transactionKey)) {
            console.log(`Transaction already in progress for ${this.gasWallet} -> ${toAddress}, waiting...`);
            // Wait for the existing transaction to complete with timeout
            let waitTime = 0;
            const maxWaitTime = 30000; // 30 seconds max wait
            while (globalTransactionLocks.has(transactionKey) && waitTime < maxWaitTime) {
                await new Promise(resolve => setTimeout(resolve, 500));
                waitTime += 500;
            }

            if (globalTransactionLocks.has(transactionKey)) {
                console.log(`Timeout waiting for previous transaction, proceeding anyway...`);
                // Force remove the stale lock
                globalTransactionLocks.delete(transactionKey);
            } else {
                console.log(`Previous transaction completed, proceeding...`);
            }
        }

        // Set transaction lock
        globalTransactionLocks.set(transactionKey, true);

        try {
            console.log("=== transferBNBFromGasWallet Debug Info ===");
            console.log(`Sending ${amount} BNB from gas wallet to ${toAddress} (attempt ${retryCount + 1}/${maxRetries + 1})`);
            console.log(`Gas wallet address: ${this.gasWallet}`);
            console.log(`Gas private key present: ${!!this.gasPrivateKey}`);
            console.log(`Gas private key length: ${this.gasPrivateKey ? this.gasPrivateKey.length : 'N/A'}`);

            // Validate inputs
            if (!this.gasWallet || !this.gasPrivateKey) {
                throw new Error("Gas wallet address or private key not configured");
            }

            if (!/^0x[a-fA-F0-9]{40}$/.test(toAddress)) {
                throw new Error(`Invalid target address format: ${toAddress}`);
            }

            if (amount <= 0) {
                throw new Error(`Invalid amount: ${amount}`);
            }

            // Convert amount to Wei for precise calculations
            const amountInWei = amount * (10 ** 18);

            // Gas limit in Wei (21000 gas for simple transfer)
            const gasLimit = 21000;

            // Gas price in Wei (3 Gwei)
            const gasPriceInWei = 3 * (10 ** 9);

            // Calculate total gas cost in Wei
            const gasCostInWei = gasLimit * gasPriceInWei;

            // Total amount needed including gas
            const totalAmountNeeded = amountInWei + gasCostInWei;

            console.log(`Amount in Wei: ${amountInWei}`);
            console.log(`Gas cost in Wei: ${gasCostInWei}`);
            console.log(`Total needed in Wei: ${totalAmountNeeded}`);

            // Check if gas wallet has enough balance
            console.log("Checking gas wallet balance...");
            const gasWalletBalance = await this.getBNBBalance(this.gasWallet);
            const gasWalletBalanceWei = gasWalletBalance * (10 ** 18);

            console.log(`Gas wallet balance: ${gasWalletBalance} BNB (${gasWalletBalanceWei} Wei)`);
            console.log(`Required amount: ${totalAmountNeeded / (10 ** 18)} BNB (${totalAmountNeeded} Wei)`);

            if (gasWalletBalanceWei < totalAmountNeeded) {
                console.warn(`Insufficient balance in gas wallet. Required: ${totalAmountNeeded / (10 ** 18)} BNB, Available: ${gasWalletBalance} BNB`);
                // Continue anyway - the transaction might still succeed if balance is close
            }

            // Remove '0x' if present from private key
            let privateKey = this.gasPrivateKey.replace('0x', '');
            console.log(`Private key length after cleaning: ${privateKey.length}`);

            // Validate private key length
            if (privateKey.length !== 64) {
                throw new Error(`Invalid private key length: ${privateKey.length}. Expected: 64 characters`);
            }

            // Validate private key format (hex)
            if (!/^[a-fA-F0-9]{64}$/.test(privateKey)) {
                throw new Error("Invalid private key format. Must be 64 hex characters");
            }

            console.log("Getting next nonce...");
            // Use fresh nonce for gas wallet transactions to minimize nonce conflicts
            const nonce = retryCount > 0 ? await this.getFreshNonce(this.gasWallet) : await this.getNextNonce(this.gasWallet);
            console.log(`Using nonce: ${nonce}`);

            const txParams = {
                nonce: '0x' + nonce.toString(16),
                to: toAddress,
                value: '0x' + Math.floor(amountInWei).toString(16),
                gas: '0x' + gasLimit.toString(16),
                gasPrice: '0x' + gasPriceInWei.toString(16),
                chainId: 56
            };

            console.log("Transaction parameters:", txParams);

            console.log("Creating Web3 instance and account...");
            const web3Instance = new Web3('https://bsc-dataseed.binance.org');

            let signedTx;
            try {
                const account = web3Instance.eth.accounts.privateKeyToAccount('0x' + privateKey);
                console.log(`Account created successfully. Address: ${account.address}`);

                // Verify the account address matches our gas wallet
                if (account.address.toLowerCase() !== this.gasWallet.toLowerCase()) {
                    throw new Error(`Private key mismatch! Expected: ${this.gasWallet}, Got: ${account.address}`);
                }

                console.log("Signing transaction...");
                signedTx = await account.signTransaction(txParams);
                console.log("Transaction signed successfully");

            } catch (accountError) {
                console.error("Error creating account or signing transaction:", accountError.message);
                throw new Error(`Account creation failed: ${accountError.message}`);
            }

            console.log("Sending transaction to blockchain...");
            // Small delay to ensure nonce consistency
            await new Promise(resolve => setTimeout(resolve, 100));
            const txHash = await this.sendRawTransaction(signedTx.rawTransaction);

            console.log("Gas transfer transaction sent! TxHash:", txHash);

            // Wait for confirmation
            const maxAttempts = 30;
            for (let i = 0; i < maxAttempts; i++) {
                const receipt = await this.getDetailedTransactionReceipt(txHash);

                if (receipt === null) {
                    console.log(`Waiting for gas transfer confirmation... Attempt ${i + 1}/${maxAttempts}`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    continue;
                }

                if (receipt.status === '0x1') {
                    console.log("Gas transfer confirmed");
                    // Lock will be released in finally block
                    return txHash;
                } else {
                    console.log("Gas transfer failed");
                    // Lock will be released in finally block
                    return false;
                }
            }

            console.log("Gas transfer confirmation timeout");
            return false;
        } catch (e) {
            console.error("Error in transferBNBFromGasWallet:", e.message);
            console.error("Error stack:", e.stack);
            console.error("Gas wallet address:", this.gasWallet);
            console.error("Gas private key present:", !!this.gasPrivateKey);
            console.error("Target address:", toAddress);
            console.error("Amount:", amount);

            // Handle nonce errors with retry
            const shouldRetry = await this.handleNonceError(this.gasWallet, e, retryCount);
            if (shouldRetry && retryCount < maxRetries) {
                console.log(`Retrying transferBNBFromGasWallet due to nonce error (attempt ${retryCount + 2}/${maxRetries + 1})`);
                // Don't release lock here - let the retry handle it
                return await this.transferBNBFromGasWallet(toAddress, amount, retryCount + 1);
            }

            return false;
        } finally {
            // Always release the transaction lock
            globalTransactionLocks.delete(transactionKey);
            console.log(`Released transaction lock for ${transactionKey}`);
        }
    }

    async sendGasFromMainWallet(toAddress) {
        try {
            const amount = 0.005; // Amount of BNB to send
            console.log("=== sendGasFromMainWallet Debug Info ===");
            console.log(`Sending ${amount} BNB from gas wallet for operations`);
            console.log(`Target address: ${toAddress}`);
            console.log(`Gas wallet: ${this.gasWallet}`);
            console.log(`Gas private key present: ${!!this.gasPrivateKey}`);

            // Validate inputs
            if (!toAddress || !/^0x[a-fA-F0-9]{40}$/.test(toAddress)) {
                throw new Error(`Invalid target address: ${toAddress}`);
            }

            if (!this.gasWallet || !this.gasPrivateKey) {
                throw new Error("Gas wallet not properly configured");
            }

            console.log("Calling transferBNBFromGasWallet...");
            const txHash = await this.transferBNBFromGasWallet(toAddress, amount);

            if (!txHash) {
                console.error("transferBNBFromGasWallet returned false/null");
                return false;
            }

            console.log(`transferBNBFromGasWallet returned txHash: ${txHash}`);

            console.log("Transaction sent successfully! TxHash:", txHash);

            // Wait for confirmation
            for (let i = 0; i < 30; i++) {
                const status = await this.getTransactionStatus(txHash);
                if (status === true) {
                    // Verify the balance was actually received
                    await new Promise(resolve => setTimeout(resolve, 5000));
                    const newBalance = await this.getBNBBalance(toAddress);
                    if (newBalance >= 0.005) {
                        return true;
                    }
                } else if (status === false) {
                    return false;
                }
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            return false;
        } catch (e) {
            console.error("Error in sendGasFromMainWallet:", e.message);
            console.error("Error stack:", e.stack);
            console.error("Gas wallet address:", this.gasWallet);
            console.error("Gas private key present:", !!this.gasPrivateKey);
            console.error("Target address:", toAddress);
            return false;
        }
    }

    async getDetailedTransactionStatus(txHash) {
        try {
            const response = await this.provider.post('', {
                jsonrpc: '2.0',
                method: 'eth_getTransactionReceipt',
                params: [txHash],
                id: 1
            });

            const receipt = response.data;

            if (!receipt.result || receipt.result === null) {
                return null; // Transaction not yet mined
            }

            const result = receipt.result;

            // Check if transaction was successful
            if (result.status === '0x1') {
                // Check for token transfer event
                for (const log of result.logs) {
                    if (log.address.toLowerCase() === this.usdtContract.toLowerCase()) {
                        // This is a USDT transfer event
                        console.log("Found USDT transfer event in transaction");
                        return true;
                    }
                }
                console.log("Transaction successful but no USDT transfer event found");
                return false;
            } else {
                console.log("Transaction failed with status:", result.status);
                return false;
            }
        } catch (e) {
            console.error("Error checking transaction status:", e.message);
            return null;
        }
    }

    async checkAllowance(owner, spender) {
        try {
            const web3 = new Web3('https://bsc-dataseed.binance.org');
            const methodID = web3.utils.sha3('allowance(address,address)').substring(0, 10);
            const param1 = owner.substring(2).padStart(64, '0');
            const param2 = spender.substring(2).padStart(64, '0');
            const data = methodID + param1 + param2;

            const response = await this.provider.post('', {
                jsonrpc: '2.0',
                method: 'eth_call',
                params: [{
                    to: this.usdtContract,
                    data: data
                }, 'latest'],
                id: 1
            });

            const body = response.data;
            return parseInt(body.result, 16) / (10 ** 18);
        } catch (e) {
            console.error("Error checking allowance:", e.message);
            return 0;
        }
    }

    async getTransactionError(txHash) {
        try {
            // Get transaction
            const response = await this.provider.post('', {
                jsonrpc: '2.0',
                method: 'eth_getTransactionByHash',
                params: [txHash],
                id: 1
            });

            const tx = response.data.result;

            // Try to simulate the transaction to get the error
            const response2 = await this.provider.post('', {
                jsonrpc: '2.0',
                method: 'eth_call',
                params: [{
                        from: tx.from,
                        to: tx.to,
                        data: tx.input,
                        value: tx.value,
                        gas: tx.gas,
                        gasPrice: tx.gasPrice
                    },
                    'latest'
                ],
                id: 1
            });

            const result = response2.data;
            return result.error ? result.error.message : 'Unknown error';
        } catch (e) {
            return e.message;
        }
    }

    async getTransactionStatus(txHash) {
        try {
            const response = await this.provider.post('', {
                jsonrpc: '2.0',
                method: 'eth_getTransactionReceipt',
                params: [txHash],
                id: 1
            });

            const receipt = response.data;

            if (!receipt.result) {
                return null; // Transaction not yet mined
            }

            if (receipt.result === null) {
                return null; // Transaction not yet mined
            }

            // Check status (1 = success, 0 = failure)
            return parseInt(receipt.result.status, 16) === 1;
        } catch (e) {
            console.error("Error checking transaction status:", e.message);
            return false;
        }
    }

    async getDetailedTransactionReceipt(txHash) {
        try {
            const response = await this.provider.post('', {
                jsonrpc: '2.0',
                method: 'eth_getTransactionReceipt',
                params: [txHash],
                id: 1
            });

            const result = response.data;

            if (!result.result) {
                return null;
            }

            return result.result;
        } catch (e) {
            console.error("Error getting transaction receipt:", e.message);
            return null;
        }
    }
}

// Export functions that can be called
async function generateNewWallet(req, res) {
    
    try {
        let auth = req.body.auth;
        const generator = new WalletGenerator();
        const wallet = generator.generateWallet();

        // Log the generated wallet (but never log the private key in production)
   
           let user_info = {
                pay_address: wallet.address,
                pay_privatekey: wallet.privateKey,
            };
            
            const [updatedCount] = await UserModel.update(user_info, {
                where: { token: auth }
            });
            
            if (updatedCount === 0) {
                console.log("Update failed: No rows affected.");
                const user = await UserModel.findOne({ where: { token: auth } });
                if (!user) {
                    console.log("No user found with the given token.");
                } else {
                    console.log("User found, but fields might not be changing or are restricted.");
                }
            } else {
                console.log(`Updated ${updatedCount} row(s).`);
            }

            return res.status(200).json({
                message: 'Wallet saved successfully',
                status: true
            });
    } catch (error) {
        console.error('Error in generateNewWallet:', error);
        return res.status(500).json({
            status: false,
            message: 'Failed to generate wallet: ' + error.message
        });
    }
}

const savewallet = async(req, res) => {
      
        try {
            let walletAddress = req.body.walletAddress;
            let walletPrivateKey = req.body.walletPrivateKey;
            let auth = req.body.auth;
            
            console.log(walletAddress, walletPrivateKey, auth)

            if (!walletAddress || !walletPrivateKey || !auth) {
                return res.status(400).json({
                    message: 'Missing required parameters',
                    status: false
                });
            }

            // Use parameterized query to prevent SQL injection
            // await connection.query(
            //     'UPDATE users SET pay_address = ?, pay_privatekey = ? WHERE token = ?', [walletAddress, walletPrivateKey, auth]
            // );
            
            let user_info = {
                pay_address: walletAddress,
                pay_privatekey: walletPrivateKey,
            };
            
            const [updatedCount] = await UserModel.update(user_info, {
                where: { token: auth }
            });
            
            if (updatedCount === 0) {
                console.log("Update failed: No rows affected.");
                const user = await UserModel.findOne({ where: { token: auth } });
                if (!user) {
                    console.log("No user found with the given token.");
                } else {
                    console.log("User found, but fields might not be changing or are restricted.");
                }
            } else {
                console.log(`Updated ${updatedCount} row(s).`);
            }

            return res.status(200).json({
                message: 'Wallet saved successfully',
                status: true
            });
        } catch (error) {
            console.error('Error saving wallet:', error);
            return res.status(500).json({
                message: 'Error saving wallet: ' + error.message,
                status: false
            });
        }
    }
    // Function to handle withdrawal requests from users
async function requestWithdrawal(req, res) {
    try {
        const { amount, walletAddress } = req.body;
        const auth = req.cookies.auth;

        if (!amount || !walletAddress || !auth) {
            return res.status(400).json({
                message: 'Missing required parameters',
                status: false
            });
        }

        // Validate wallet address format
        if (!/^0x[a-fA-F0-9]{40}$/.test(walletAddress)) {
            return res.status(400).json({
                message: 'Invalid wallet address format',
                status: false
            });
        }

        // Get user information
        const [user] = await connection.query('SELECT * FROM users WHERE token = ?', [auth]);
        if (user.length === 0) {
            return res.status(401).json({
                message: 'User not found',
                status: false
            });
        }

        const userInfo = user[0];
        const money = parseFloat(amount);

        // Check if user has enough balance
        if (userInfo.money < money) {
            return res.status(400).json({
                message: 'Insufficient balance',
                status: false
            });
        }

        // Generate a unique ID for the withdrawal
        const id_time = Math.floor(Date.now() / 1000);
        const id_order = Math.floor(Math.random() * 1000000);
        const checkTime = new Date().toISOString().split('T')[0];
        const dates = Date.now();

        // Insert withdrawal request into database
        const sql = `INSERT INTO withdraw SET
            id_order = ?,
            phone = ?,
            money = ?,
            mode = ?,
            address = ?,
            status = ?,
            today = ?,
            time = ?`;

        await connection.execute(sql, [
            id_time + '' + id_order,
            userInfo.phone,
            money,
            'USDT',
            walletAddress,
            0,
            checkTime,
            dates
        ]);

        // Deduct the amount from user's balance
        await connection.query('UPDATE users SET money = money - ? WHERE phone = ?', [money, userInfo.phone]);

        return res.status(200).json({
            message: 'Withdrawal request submitted successfully',
            status: true,
            money: userInfo.money - money
        });

    } catch (error) {
        console.error('Error in requestWithdrawal:', error);
        return res.status(500).json({
            message: 'Error processing withdrawal request: ' + error.message,
            status: false
        });
    }
}

// Function for admin to process withdrawal requests
async function processWithdrawal(req, res) {
    try {
        const { id } = req.body;

        if (!id) {
            return res.status(400).json({
                message: 'Missing withdrawal ID',
                status: false
            });
        }

        // Get admin private key from database
        const [adminKeyResult] = await connection.query("SELECT `pkey` FROM `admin` WHERE 1");

        if (!adminKeyResult || adminKeyResult.length === 0 || !adminKeyResult[0].pkey) {
            return res.status(400).json({
                message: 'Admin private key not configured',
                status: false
            });
        }

        // Ensure adminPrivateKey is a string
        const adminPrivateKey = String(adminKeyResult[0].pkey);
        console.log('Admin private key type:', typeof adminPrivateKey);

        // Get withdrawal request details
        const [withdrawalRequest] = await connection.query('SELECT * FROM withdraw WHERE id = ?', [id]);
        if (withdrawalRequest.length === 0) {
            return res.status(404).json({
                message: 'Withdrawal request not found',
                status: false
            });
        }

        const withdrawal = withdrawalRequest[0];

        // Check if withdrawal is already processed
        if (withdrawal.status !== 0) {
            return res.status(400).json({
                message: 'Withdrawal request already processed',
                status: false
            });
        }

        // Convert INR to USDT (assuming 1 USDT = 90 INR as per your existing code)
        const usdtAmount = withdrawal.money / 90;

        // Log the withdrawal details for debugging
        console.log('Processing withdrawal:', {
            id: withdrawal.id,
            amount: withdrawal.money,
            usdtAmount,
            address: withdrawal.address,
            mode: withdrawal.mode
        });

        // USDT contract address on BSC
        const usdtContract = '******************************************';

        // Create a web3 instance
        const web3 = new Web3('https://bsc-dataseed.binance.org');

        // Derive admin wallet address from private key
        try {
            // Ensure the private key has the correct format (0x prefix)
            let cleanPrivateKey = adminPrivateKey;
            if (!cleanPrivateKey.startsWith('0x')) {
                cleanPrivateKey = '0x' + cleanPrivateKey;
            }

            console.log('Using private key format:', cleanPrivateKey.substring(0, 6) + '...');

            const account = web3.eth.accounts.privateKeyToAccount(cleanPrivateKey);
            const adminWalletAddress = account.address;
            console.log(`Using admin wallet address: ${adminWalletAddress}`);

            // Create contract ABI for the transfer function
            const transferAbi = {
                name: 'transfer',
                type: 'function',
                inputs: [
                    { type: 'address', name: 'recipient' },
                    { type: 'uint256', name: 'amount' }
                ]
            };

            // Validate the destination address
            if (!withdrawal.address || !/^0x[a-fA-F0-9]{40}$/.test(withdrawal.address)) {
                return res.status(400).json({
                    message: 'Invalid destination wallet address',
                    status: false
                });
            }

            console.log(`Sending ${usdtAmount} USDT to ${withdrawal.address}`);

            // Encode the function call
            const transactionData = web3.eth.abi.encodeFunctionCall(transferAbi, [
                withdrawal.address,
                web3.utils.toWei(usdtAmount.toString(), 'ether')
            ]);

            // Get the nonce for the admin wallet
            const nonce = await web3.eth.getTransactionCount(adminWalletAddress, 'latest');

            // Prepare transaction parameters
            const txParams = {
                nonce: '0x' + nonce.toString(16),
                to: usdtContract,
                value: '0x0',
                data: transactionData,
                gas: '0x186A0', // 100000 gas
                gasPrice: '0x' + (3 * 10 ** 9).toString(16), // 3 Gwei
                chainId: 56 // BSC mainnet
            };

            // Sign the transaction using the already created account
            const signedTx = await account.signTransaction(txParams);

            try {
                // Send the transaction
                console.log('Sending transaction...');
                const receipt = await web3.eth.sendSignedTransaction(signedTx.rawTransaction);
                console.log('Transaction receipt:', receipt);

                if (receipt.status) {
                    // Update withdrawal status in database
                    await connection.query('UPDATE withdraw SET status = 1, txn_hash = ? WHERE id = ?', [receipt.transactionHash, id]);

                    return res.status(200).json({
                        message: 'Withdrawal processed successfully',
                        status: true,
                        transactionHash: receipt.transactionHash
                    });
                } else {
                    // If transaction failed, revert user's balance
                    await connection.query('UPDATE users SET money = money + ? WHERE phone = ?', [withdrawal.money, withdrawal.phone]);
                    await connection.query('UPDATE withdraw SET status = 2 WHERE id = ?', [id]);

                    return res.status(500).json({
                        message: 'Transaction failed',
                        status: false
                    });
                }
            } catch (txError) {
                console.error('Transaction error:', txError);

                // Revert user's balance
                await connection.query('UPDATE users SET money = money + ? WHERE phone = ?', [withdrawal.money, withdrawal.phone]);
                await connection.query('UPDATE withdraw SET status = 2 WHERE id = ?', [id]);

                return res.status(500).json({
                    message: 'Transaction error: ' + txError.message,
                    status: false
                });
            }
        } catch (error) {
            console.error('Error in processWithdrawal:', error);

            // If there's an error, check if we need to revert the user's balance
            try {
                const [withdrawalRequest] = await connection.query('SELECT * FROM withdraw WHERE id = ?', [req.body.id]);
                if (withdrawalRequest.length > 0 && withdrawalRequest[0].status === 0) {
                    // Revert user's balance
                    await connection.query('UPDATE users SET money = money + ? WHERE phone = ?', [withdrawalRequest[0].money, withdrawalRequest[0].phone]);
                    await connection.query('UPDATE withdraw SET status = 2 WHERE id = ?', [req.body.id]);
                }
            } catch (revertError) {
                console.error('Error reverting user balance:', revertError);
            }

            return res.status(500).json({
                message: 'Error processing withdrawal: ' + error.message,
                status: false
            });
        }
    }
 catch (error) {
    console.error('Error in processWithdrawal:', error);
    return res.status(500).json({
        message: 'Error processing withdrawal: ' + error.message,
        status: false
    });
}}

async function startMonitoring(req, res) {
    try {
        const { walletAddress, walletPrivateKey } = req.body;

        if (!walletAddress || !walletPrivateKey) {
            return res.status(400).json({
                status: false,
                message: 'Wallet address and private key are required'
            });
        }

        // Get wallet addresses from environment variables or config
        // You should set these in your environment or config file
        const usdtReceiveWallet = process.env.USDT_RECEIVE_WALLET || '0xYourUsdtReceiveWalletAddress'; // Replace with your wallet

        // Gas wallet private key - make sure this is correct
        const gasPrivateKey = process.env.GAS_PRIVATE_KEY || '51adcbb263565e60e4a4e50cd967d5d044c99ffdc7dc0bfc0a370fd264f4b98b';

        // Derive gas wallet address from private key to ensure they match
        let gasWallet;
        try {
            console.log("=== Gas Wallet Configuration Validation ===");
            const web3 = new Web3('https://bsc-dataseed.binance.org');
            let cleanPrivateKey = gasPrivateKey;
            if (!cleanPrivateKey.startsWith('0x')) {
                cleanPrivateKey = '0x' + cleanPrivateKey;
            }

            console.log("Private key length:", gasPrivateKey.length);
            console.log("Private key format (first 6 chars):", gasPrivateKey.substring(0, 6));

            const account = web3.eth.accounts.privateKeyToAccount(cleanPrivateKey);
            gasWallet = account.address;
            console.log(`✅ Derived gas wallet address from private key: ${gasWallet}`);

            // Check gas wallet balance
            const provider = axios.create({
                baseURL: 'https://bsc-dataseed.binance.org'
            });

            const balanceResponse = await provider.post('', {
                jsonrpc: '2.0',
                method: 'eth_getBalance',
                params: [gasWallet, 'latest'],
                id: 1
            });

            const balanceWei = balanceResponse.data.result;
            const balanceBNB = parseInt(balanceWei, 16) / (10 ** 18);

            console.log(`Gas wallet BNB balance: ${balanceBNB} BNB`);

            if (balanceBNB < 0.01) {
                console.warn(`⚠️  WARNING: Gas wallet has low balance (${balanceBNB} BNB). Minimum recommended: 0.01 BNB`);
                console.warn(`Please send BNB to gas wallet: ${gasWallet}`);
            } else {
                console.log(`✅ Gas wallet has sufficient balance: ${balanceBNB} BNB`);
            }

        } catch (error) {
            console.error("❌ Error deriving gas wallet address from private key:", error.message);
            console.error("Error stack:", error.stack);
            throw new Error("Invalid gas wallet private key configuration: " + error.message);
        }

        console.log("Starting monitoring with:");
        console.log("USDT Receive Wallet:", usdtReceiveWallet);
        console.log("Gas Wallet:", gasWallet);
        console.log("Monitored Wallet:", walletAddress);

        console.log("Creating WalletMonitor instance with parameters:");
        console.log("USDT Receive Wallet:", usdtReceiveWallet);
        console.log("Gas Wallet:", gasWallet);
        console.log("Gas Private Key:", gasPrivateKey.substring(0, 6) + "..." + gasPrivateKey.substring(gasPrivateKey.length - 4)); // Only show part of the private key for security

        let monitor;
        const wallet = {
            address: walletAddress,
            privateKey: walletPrivateKey
        };

        try {
            monitor = new WalletMonitor(usdtReceiveWallet, gasWallet, gasPrivateKey);
            console.log("WalletMonitor instance created successfully!");
            console.log("Wallet object created with address:", walletAddress);
        } catch (error) {
            console.error("Error creating WalletMonitor instance:", error.message);
            console.error("Error stack:", error.stack);
            throw error;
        }

        const result = await monitor.monitorAndTransfer(wallet);

        return res.status(200).json({
            status: true,
            result: result,
            message: 'Monitoring completed successfully'
        });
    } catch (e) {
        console.error("Error in startMonitoring:", e.message);
        return res.status(500).json({
            status: false,
            message: 'Error monitoring wallet: ' + e.message
        });
    }
}

async function getRechargeOption(req,res){
    
    try{
         const { rechargeOption, auth } = req.body;
          if (!rechargeOption ) {
            return res.status(400).json({
                status: false,
                message: 'Recharge Option is required'
            });
        }
        
            let user_info = {
                rechargeOption : rechargeOption
            };
            
            const [updatedCount] = await UserModel.update(user_info, {
                where: { token: auth }
            });
            
            if (updatedCount === 0) {
                console.log("Update failed: No rows affected.");
                const user = await UserModel.findOne({ where: { token: auth } });
                if (!user) {
                    console.log("No user found with the given token.");
                } else {
                    console.log("User found, but fields might not be changing or are restricted.");
                }
            } else {
                console.log(`Updated ${updatedCount} row(s).`);
            }

            return res.status(200).json({
                message: 'Recharge Option Updated successfully',
                status: true
            });
        
        
    }catch(err){
            return res.status(500).json({
                message: 'Error saving Recharge Option: ' + err.message,
                status: false
            });
    }
}

module.exports = {
    generateNewWallet,
    startMonitoring,
    WalletGenerator,
    WalletMonitor,
    savewallet,
    getRechargeOption,
    requestWithdrawal,
    processWithdrawal
};