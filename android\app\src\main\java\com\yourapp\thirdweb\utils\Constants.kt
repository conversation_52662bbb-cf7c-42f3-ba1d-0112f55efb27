package com.yourapp.thirdweb.utils

object Constants {
    
    // Network Configuration
    object Networks {
        const val ETHEREUM_MAINNET = "ethereum"
        const val ETHEREUM_GOERLI = "goerli"
        const val POLYGON_MAINNET = "polygon"
        const val POLYGON_MUMBAI = "mumbai"
        const val BSC_MAINNET = "binance"
        const val BSC_TESTNET = "binance-testnet"
    }
    
    // Common Contract Addresses
    object Contracts {
        // Ethereum Mainnet
        const val USDC_ETHEREUM = "******************************************"
        const val USDT_ETHEREUM = "******************************************"
        
        // Polygon Mainnet
        const val USDC_POLYGON = "******************************************"
        const val USDT_POLYGON = "******************************************"
        
        // BSC Mainnet
        const val USDT_BSC = "******************************************"
        const val BUSD_BSC = "******************************************"
    }
    
    // Wallet Configuration
    object Wallet {
        const val WALLET_CONNECT_PROJECT_ID = "your_walletconnect_project_id"
        const val APP_NAME = "ThirdWeb Kotlin App"
        const val APP_DESCRIPTION = "A Kotlin Android app with thirdweb integration"
        const val APP_URL = "https://yourapp.com"
        const val APP_ICON = "https://yourapp.com/icon.png"
    }
    
    // API Configuration
    object API {
        const val THIRDWEB_CLIENT_ID = "your_thirdweb_client_id"
        const val THIRDWEB_SECRET_KEY = "your_thirdweb_secret_key"
    }
    
    // Transaction Configuration
    object Transaction {
        const val DEFAULT_GAS_LIMIT = 21000L
        const val DEFAULT_GAS_PRICE = 20000000000L // 20 Gwei
        const val CONFIRMATION_BLOCKS = 3
    }
    
    // UI Configuration
    object UI {
        const val ANIMATION_DURATION = 300
        const val DEBOUNCE_DELAY = 500L
    }
}
