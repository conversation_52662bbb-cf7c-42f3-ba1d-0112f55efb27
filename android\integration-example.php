<?php
/**
 * Example PHP backend integration for crypto payments
 * This shows how to integrate the Kotlin app with your existing PHP backend
 */

class CryptoPaymentController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('User_model');
        $this->load->model('Payment_model');
        $this->load->model('Setting_model');
    }

    /**
     * Register wallet address with user account
     */
    public function register_wallet()
    {
        $userId = $this->input->post('userId');
        $walletAddress = $this->input->post('walletAddress');
        $walletType = $this->input->post('walletType');

        // Validate input
        if (empty($userId) || empty($walletAddress)) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'User ID and wallet address are required'
                ]));
            return;
        }

        // Validate Ethereum address format
        if (!$this->isValidEthereumAddress($walletAddress)) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'Invalid wallet address format'
                ]));
            return;
        }

        // Check if user exists
        $user = $this->User_model->get_user_by_id($userId);
        if (!$user) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'User not found'
                ]));
            return;
        }

        // Save wallet address
        $walletData = [
            'user_id' => $userId,
            'wallet_address' => $walletAddress,
            'wallet_type' => $walletType,
            'created_at' => date('Y-m-d H:i:s'),
            'status' => 'active'
        ];

        $walletId = $this->Payment_model->save_wallet($walletData);

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'success' => true,
                'message' => 'Wallet registered successfully',
                'data' => [
                    'walletId' => $walletId
                ]
            ]));
    }

    /**
     * Verify crypto payment transaction
     */
    public function verify_crypto_payment()
    {
        $userId = $this->input->post('userId');
        $transactionHash = $this->input->post('transactionHash');
        $amount = $this->input->post('amount');
        $currency = $this->input->post('currency');
        $network = $this->input->post('network');
        $walletAddress = $this->input->post('walletAddress');

        // Validate input
        if (empty($transactionHash) || empty($amount) || empty($userId)) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'Missing required fields'
                ]));
            return;
        }

        // Check if transaction already processed
        $existingPayment = $this->Payment_model->get_payment_by_hash($transactionHash);
        if ($existingPayment) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'Transaction already processed'
                ]));
            return;
        }

        // Verify transaction on blockchain
        $verification = $this->verifyBlockchainTransaction($transactionHash, $network, $amount, $currency);

        if ($verification['verified']) {
            // Save payment record
            $paymentData = [
                'user_id' => $userId,
                'transaction_hash' => $transactionHash,
                'amount' => $amount,
                'currency' => $currency,
                'network' => $network,
                'wallet_address' => $walletAddress,
                'status' => 'confirmed',
                'confirmations' => $verification['confirmations'],
                'created_at' => date('Y-m-d H:i:s')
            ];

            $paymentId = $this->Payment_model->save_crypto_payment($paymentData);

            // Update user balance
            $this->updateUserBalance($userId, $amount, $transactionHash);

            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => true,
                    'message' => 'Payment verified and balance updated',
                    'data' => [
                        'verified' => true,
                        'transactionStatus' => 'confirmed',
                        'confirmations' => $verification['confirmations'],
                        'amountReceived' => $amount,
                        'balanceUpdated' => true
                    ]
                ]));
        } else {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'Transaction verification failed',
                    'data' => [
                        'verified' => false,
                        'transactionStatus' => 'failed'
                    ]
                ]));
        }
    }

    /**
     * Get crypto payment options based on settings
     */
    public function get_crypto_options()
    {
        $settings = $this->Setting_model->Setting();
        
        $options = [];
        
        // Check if crypto payments are enabled
        if ($settings->payment_mode === 'auto' || $settings->manual_payment_type === 'USDT') {
            $options[] = [
                'currency' => 'USDT',
                'network' => 'BSC',
                'contractAddress' => '******************************************',
                'minAmount' => '1',
                'maxAmount' => '10000',
                'enabled' => true
            ];
            
            $options[] = [
                'currency' => 'ETH',
                'network' => 'Ethereum',
                'contractAddress' => null,
                'minAmount' => '0.001',
                'maxAmount' => '10',
                'enabled' => true
            ];
        }

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'success' => true,
                'data' => $options
            ]));
    }

    /**
     * Update user balance after successful crypto payment
     */
    public function update_balance()
    {
        $userId = $this->input->post('userId');
        $amount = $this->input->post('amount');
        $transactionHash = $this->input->post('transactionHash');

        $result = $this->updateUserBalance($userId, $amount, $transactionHash);

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($result));
    }

    /**
     * Private helper methods
     */
    private function isValidEthereumAddress($address)
    {
        return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
    }

    private function verifyBlockchainTransaction($txHash, $network, $expectedAmount, $currency)
    {
        // This is a simplified example
        // In production, you should use proper blockchain APIs like:
        // - Etherscan API for Ethereum
        // - BSCScan API for BSC
        // - Polygonscan API for Polygon
        
        // Example using a generic approach
        $rpcUrl = $this->getRpcUrl($network);
        
        // Make RPC call to get transaction details
        $transactionData = $this->makeRpcCall($rpcUrl, 'eth_getTransactionByHash', [$txHash]);
        
        if ($transactionData && isset($transactionData['result'])) {
            $tx = $transactionData['result'];
            
            // Verify transaction details
            $verified = true; // Implement your verification logic here
            $confirmations = 12; // Get actual confirmations from blockchain
            
            return [
                'verified' => $verified,
                'confirmations' => $confirmations
            ];
        }
        
        return [
            'verified' => false,
            'confirmations' => 0
        ];
    }

    private function getRpcUrl($network)
    {
        $rpcUrls = [
            'ethereum' => 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY',
            'bsc' => 'https://bsc-dataseed.binance.org',
            'polygon' => 'https://polygon-rpc.com'
        ];
        
        return $rpcUrls[$network] ?? $rpcUrls['ethereum'];
    }

    private function makeRpcCall($url, $method, $params)
    {
        $data = [
            'jsonrpc' => '2.0',
            'method' => $method,
            'params' => $params,
            'id' => 1
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }

    private function updateUserBalance($userId, $amount, $transactionHash)
    {
        // Get current user balance
        $user = $this->User_model->get_user_by_id($userId);
        if (!$user) {
            return [
                'success' => false,
                'message' => 'User not found'
            ];
        }

        // Convert crypto amount to your app's currency
        $convertedAmount = $this->convertCryptoToAppCurrency($amount);
        
        // Update user balance
        $newBalance = $user->balance + $convertedAmount;
        
        $updateData = [
            'balance' => $newBalance
        ];
        
        $this->User_model->update_user($userId, $updateData);

        // Log the transaction
        $this->logBalanceUpdate($userId, $convertedAmount, $transactionHash, 'crypto_payment');

        return [
            'success' => true,
            'newBalance' => $newBalance,
            'message' => 'Balance updated successfully'
        ];
    }

    private function convertCryptoToAppCurrency($cryptoAmount)
    {
        // Implement your conversion logic here
        // This could involve calling a price API or using fixed rates
        return floatval($cryptoAmount) * 100; // Example: 1 USDT = 100 app coins
    }

    private function logBalanceUpdate($userId, $amount, $reference, $type)
    {
        $logData = [
            'user_id' => $userId,
            'amount' => $amount,
            'type' => $type,
            'reference' => $reference,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $this->Payment_model->log_balance_update($logData);
    }
}
