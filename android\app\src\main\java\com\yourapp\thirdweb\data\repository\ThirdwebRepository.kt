package com.yourapp.thirdweb.data.repository

import android.content.Context
import com.thirdweb.sdk.ThirdwebSDK
import com.thirdweb.sdk.core.Chain
import com.thirdweb.sdk.core.Wallet
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ThirdwebRepository @Inject constructor(
    private val context: Context
) {
    
    private var sdk: ThirdwebSDK? = null
    private val _connectionState = MutableStateFlow<ConnectionState>(ConnectionState.Disconnected)
    val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()
    
    private val _walletAddress = MutableStateFlow<String?>(null)
    val walletAddress: StateFlow<String?> = _walletAddress.asStateFlow()
    
    private val _balance = MutableStateFlow<String>("0")
    val balance: StateFlow<String> = _balance.asStateFlow()
    
    /**
     * Initialize thirdweb SDK with specified chain
     */
    suspend fun initializeSDK(chain: Chain = Chain.Ethereum): Result<ThirdwebSDK> {
        return try {
            sdk = ThirdwebSDK(context, chain)
            _connectionState.value = ConnectionState.Initialized
            Result.success(sdk!!)
        } catch (e: Exception) {
            _connectionState.value = ConnectionState.Error(e.message ?: "Failed to initialize SDK")
            Result.failure(e)
        }
    }
    
    /**
     * Connect wallet using various methods
     */
    suspend fun connectWallet(walletType: WalletType): Result<String> {
        return try {
            val currentSdk = sdk ?: return Result.failure(Exception("SDK not initialized"))
            
            _connectionState.value = ConnectionState.Connecting
            
            val wallet = when (walletType) {
                WalletType.MetaMask -> {
                    // Connect to MetaMask
                    currentSdk.wallet.connectWallet(Wallet.MetaMask())
                }
                WalletType.WalletConnect -> {
                    // Connect via WalletConnect
                    currentSdk.wallet.connectWallet(Wallet.WalletConnect())
                }
                WalletType.LocalWallet -> {
                    // Create or connect local wallet
                    currentSdk.wallet.connectWallet(Wallet.LocalWallet())
                }
            }
            
            val address = currentSdk.wallet.getAddress()
            _walletAddress.value = address
            _connectionState.value = ConnectionState.Connected
            
            // Update balance after connection
            updateBalance()
            
            Result.success(address)
        } catch (e: Exception) {
            _connectionState.value = ConnectionState.Error(e.message ?: "Failed to connect wallet")
            Result.failure(e)
        }
    }
    
    /**
     * Disconnect wallet
     */
    suspend fun disconnectWallet(): Result<Unit> {
        return try {
            sdk?.wallet?.disconnect()
            _walletAddress.value = null
            _balance.value = "0"
            _connectionState.value = ConnectionState.Disconnected
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get current wallet balance
     */
    suspend fun updateBalance(): Result<String> {
        return try {
            val currentSdk = sdk ?: return Result.failure(Exception("SDK not initialized"))
            val balance = currentSdk.wallet.getBalance()
            _balance.value = balance.displayValue
            Result.success(balance.displayValue)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Send transaction
     */
    suspend fun sendTransaction(
        to: String,
        amount: String,
        data: String = "0x"
    ): Result<String> {
        return try {
            val currentSdk = sdk ?: return Result.failure(Exception("SDK not initialized"))
            
            val transaction = currentSdk.wallet.sendTransaction(
                to = to,
                value = amount,
                data = data
            )
            
            // Update balance after transaction
            updateBalance()
            
            Result.success(transaction.hash)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get contract instance
     */
    suspend fun getContract(contractAddress: String): Result<Any> {
        return try {
            val currentSdk = sdk ?: return Result.failure(Exception("SDK not initialized"))
            val contract = currentSdk.getContract(contractAddress)
            Result.success(contract)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Sign message
     */
    suspend fun signMessage(message: String): Result<String> {
        return try {
            val currentSdk = sdk ?: return Result.failure(Exception("SDK not initialized"))
            val signature = currentSdk.wallet.sign(message)
            Result.success(signature)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

sealed class ConnectionState {
    object Disconnected : ConnectionState()
    object Initialized : ConnectionState()
    object Connecting : ConnectionState()
    object Connected : ConnectionState()
    data class Error(val message: String) : ConnectionState()
}

enum class WalletType {
    MetaMask,
    WalletConnect,
    LocalWallet
}
