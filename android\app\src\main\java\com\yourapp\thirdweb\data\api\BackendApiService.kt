package com.yourapp.thirdweb.data.api

import retrofit2.Response
import retrofit2.http.*

/**
 * API service for communicating with your PHP backend
 * This integrates with your existing payment system
 */
interface BackendApiService {
    
    /**
     * Register wallet address with user account
     */
    @POST("api/wallet/register")
    suspend fun registerWallet(
        @Body request: RegisterWalletRequest
    ): Response<ApiResponse<WalletRegistrationResponse>>
    
    /**
     * Verify crypto payment transaction
     */
    @POST("api/payment/verify-crypto")
    suspend fun verifyCryptoPayment(
        @Body request: VerifyPaymentRequest
    ): Response<ApiResponse<PaymentVerificationResponse>>
    
    /**
     * Get user's crypto payment history
     */
    @GET("api/payment/crypto-history/{userId}")
    suspend fun getCryptoPaymentHistory(
        @Path("userId") userId: String
    ): Response<ApiResponse<List<CryptoPayment>>>
    
    /**
     * Update user balance after successful crypto payment
     */
    @POST("api/user/update-balance")
    suspend fun updateUserBalance(
        @Body request: UpdateBalanceRequest
    ): Response<ApiResponse<BalanceUpdateResponse>>
    
    /**
     * Get supported crypto payment options
     */
    @GET("api/payment/crypto-options")
    suspend fun getCryptoPaymentOptions(): Response<ApiResponse<List<CryptoPaymentOption>>>
}

// Request/Response Data Classes

data class RegisterWalletRequest(
    val userId: String,
    val walletAddress: String,
    val walletType: String // "metamask", "walletconnect", "local"
)

data class WalletRegistrationResponse(
    val success: Boolean,
    val message: String,
    val walletId: String?
)

data class VerifyPaymentRequest(
    val userId: String,
    val transactionHash: String,
    val amount: String,
    val currency: String, // "ETH", "USDT", "USDC", etc.
    val network: String,  // "ethereum", "polygon", "bsc"
    val walletAddress: String
)

data class PaymentVerificationResponse(
    val verified: Boolean,
    val transactionStatus: String,
    val confirmations: Int,
    val amountReceived: String,
    val balanceUpdated: Boolean,
    val message: String
)

data class CryptoPayment(
    val id: String,
    val transactionHash: String,
    val amount: String,
    val currency: String,
    val network: String,
    val status: String, // "pending", "confirmed", "failed"
    val confirmations: Int,
    val timestamp: String,
    val walletAddress: String
)

data class UpdateBalanceRequest(
    val userId: String,
    val amount: String,
    val transactionHash: String,
    val paymentType: String = "crypto"
)

data class BalanceUpdateResponse(
    val success: Boolean,
    val newBalance: String,
    val message: String
)

data class CryptoPaymentOption(
    val currency: String,
    val network: String,
    val contractAddress: String?,
    val minAmount: String,
    val maxAmount: String,
    val enabled: Boolean
)

data class ApiResponse<T>(
    val success: Boolean,
    val data: T?,
    val message: String,
    val error: String?
)
