# ThirdWeb Kotlin Android Integration

A complete Kotlin Android application with thirdweb SDK integration for blockchain interactions.

## Features

- 🔗 **Wallet Connection**: Support for MetaMask, WalletConnect, and Local Wallet
- 💰 **Balance Management**: View and refresh wallet balances
- 📤 **Transactions**: Send ETH and interact with smart contracts
- ✍️ **Message Signing**: Sign messages with connected wallet
- 🎨 **Modern UI**: Built with Jetpack Compose and Material 3
- 🏗️ **Clean Architecture**: MVVM pattern with Hilt dependency injection

## Prerequisites

- Android Studio Arctic Fox or later
- Kotlin 1.9.10+
- Android SDK 24+
- thirdweb account (for API keys)

## Setup Instructions

### 1. Clone and Setup Project

```bash
# Navigate to your project directory
cd your-project-directory

# Open the android folder in Android Studio
```

### 2. Configure thirdweb

1. **Get thirdweb API Keys**:
   - Visit [thirdweb.com](https://thirdweb.com)
   - Create an account and get your Client ID
   - Note your project details

2. **Update Constants.kt**:
   ```kotlin
   // In android/app/src/main/java/com/yourapp/thirdweb/utils/Constants.kt
   object API {
       const val THIRDWEB_CLIENT_ID = "your_actual_client_id_here"
       const val THIRDWEB_SECRET_KEY = "your_actual_secret_key_here"
   }
   ```

### 3. Configure WalletConnect (Optional)

1. **Get WalletConnect Project ID**:
   - Visit [cloud.walletconnect.com](https://cloud.walletconnect.com)
   - Create a project and get your Project ID

2. **Update Constants.kt**:
   ```kotlin
   object Wallet {
       const val WALLET_CONNECT_PROJECT_ID = "your_walletconnect_project_id"
       // ... other configurations
   }
   ```

### 4. Update Package Name

1. **Change package name** in all files from `com.yourapp.thirdweb` to your desired package name
2. **Update AndroidManifest.xml** with your package name
3. **Update build.gradle** applicationId

### 5. Build and Run

```bash
# In Android Studio
# 1. Sync project with Gradle files
# 2. Build the project
# 3. Run on device or emulator
```

## Project Structure

```
android/
├── app/
│   ├── src/main/java/com/yourapp/thirdweb/
│   │   ├── data/
│   │   │   ├── contract/          # Smart contract interactions
│   │   │   └── repository/        # Data layer
│   │   ├── di/                    # Dependency injection
│   │   ├── ui/
│   │   │   ├── theme/            # UI theming
│   │   │   └── wallet/           # Wallet UI components
│   │   ├── utils/                # Utility functions
│   │   ├── MainActivity.kt       # Main activity
│   │   └── ThirdwebApplication.kt # Application class
│   ├── AndroidManifest.xml
│   └── build.gradle
└── build.gradle
```

## Key Components

### 1. ThirdwebRepository
Handles all blockchain interactions:
- SDK initialization
- Wallet connections
- Transaction management
- Balance updates

### 2. WalletViewModel
Manages UI state and business logic:
- Connection state management
- Transaction handling
- Error management

### 3. WalletScreen
Main UI component with:
- Wallet connection options
- Balance display
- Transaction dialogs
- Message signing

### 4. ContractInteractor
Utility for smart contract interactions:
- ERC20 token operations
- NFT operations
- Custom contract calls

## Usage Examples

### Connect Wallet
```kotlin
// In your ViewModel or Repository
viewModel.connectWallet(WalletType.MetaMask)
```

### Send Transaction
```kotlin
// Send ETH to an address
viewModel.sendTransaction(
    to = "0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b",
    amount = "0.1" // ETH amount
)
```

### Interact with Smart Contracts
```kotlin
// Get ERC20 token balance
val balance = contractInteractor.getTokenBalance(
    sdk = sdk,
    contractAddress = "0x...", // Token contract address
    walletAddress = walletAddress
)
```

## Supported Networks

- Ethereum Mainnet
- Ethereum Goerli (Testnet)
- Polygon Mainnet
- Polygon Mumbai (Testnet)
- Binance Smart Chain
- Binance Smart Chain Testnet

## Integration with Your Backend

To integrate with your existing PHP backend (like the payment system in your workspace):

1. **API Endpoints**: Create endpoints in your PHP backend to handle:
   - Wallet address registration
   - Transaction verification
   - Payment confirmations

2. **Database Updates**: Modify your existing payment logic to support crypto payments:
   ```php
   // In your PHP backend
   if ($payment_mode === 'crypto') {
       // Handle crypto payment verification
       // Update user balance after blockchain confirmation
   }
   ```

3. **Webhook Integration**: Set up webhooks to listen for blockchain events

## Security Considerations

- Never store private keys in the app
- Use secure storage for sensitive data
- Validate all transactions on your backend
- Implement proper error handling
- Use testnet for development

## Troubleshooting

### Common Issues

1. **Build Errors**: Make sure all dependencies are properly synced
2. **Network Issues**: Check internet connection and RPC endpoints
3. **Wallet Connection**: Ensure MetaMask or other wallets are installed
4. **Transaction Failures**: Check gas fees and network congestion

### Debug Mode

Enable debug logging in your Application class:
```kotlin
// In ThirdwebApplication.kt
override fun onCreate() {
    super.onCreate()
    if (BuildConfig.DEBUG) {
        // Enable debug logging
    }
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
- Check thirdweb documentation: [docs.thirdweb.com](https://docs.thirdweb.com)
- Join thirdweb Discord community
- Create issues in this repository
