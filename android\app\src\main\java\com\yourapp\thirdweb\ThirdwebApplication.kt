package com.yourapp.thirdweb

import android.app.Application
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class ThirdwebApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize any global configurations here
        initializeThirdweb()
    }
    
    private fun initializeThirdweb() {
        // Global thirdweb initialization if needed
        // This can include setting up default configurations
    }
}
