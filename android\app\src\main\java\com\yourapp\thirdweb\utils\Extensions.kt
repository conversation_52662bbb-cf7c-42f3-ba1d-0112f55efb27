package com.yourapp.thirdweb.utils

import java.math.BigDecimal
import java.math.BigInteger
import java.text.DecimalFormat

/**
 * Extension functions for blockchain-related operations
 */

/**
 * Convert Wei to Ether
 */
fun BigInteger.weiToEther(): BigDecimal {
    return BigDecimal(this).divide(BigDecimal("1000000000000000000"))
}

/**
 * Convert Ether to Wei
 */
fun BigDecimal.etherToWei(): BigInteger {
    return this.multiply(BigDecimal("1000000000000000000")).toBigInteger()
}

/**
 * Format address for display (show first 6 and last 4 characters)
 */
fun String.formatAddress(): String {
    return if (this.length >= 10) {
        "${this.take(6)}...${this.takeLast(4)}"
    } else {
        this
    }
}

/**
 * Validate Ethereum address format
 */
fun String.isValidEthereumAddress(): <PERSON><PERSON>an {
    return this.matches(Regex("^0x[a-fA-F0-9]{40}$"))
}

/**
 * Format balance for display
 */
fun String.formatBalance(decimals: Int = 4): String {
    return try {
        val number = this.toDouble()
        val formatter = DecimalFormat("#.${"#".repeat(decimals)}")
        formatter.format(number)
    } catch (e: Exception) {
        this
    }
}

/**
 * Convert hex string to decimal
 */
fun String.hexToDecimal(): BigInteger {
    return BigInteger(this.removePrefix("0x"), 16)
}

/**
 * Convert decimal to hex string
 */
fun BigInteger.toHexString(): String {
    return "0x${this.toString(16)}"
}

/**
 * Check if string is a valid private key
 */
fun String.isValidPrivateKey(): Boolean {
    val cleanKey = this.removePrefix("0x")
    return cleanKey.length == 64 && cleanKey.matches(Regex("[a-fA-F0-9]+"))
}

/**
 * Safe conversion to BigDecimal
 */
fun String.toBigDecimalOrNull(): BigDecimal? {
    return try {
        BigDecimal(this)
    } catch (e: Exception) {
        null
    }
}
