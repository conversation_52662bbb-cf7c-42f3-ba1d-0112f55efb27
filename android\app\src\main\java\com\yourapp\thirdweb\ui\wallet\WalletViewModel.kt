package com.yourapp.thirdweb.ui.wallet

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thirdweb.sdk.core.Chain
import com.yourapp.thirdweb.data.repository.ConnectionState
import com.yourapp.thirdweb.data.repository.ThirdwebRepository
import com.yourapp.thirdweb.data.repository.WalletType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class WalletViewModel @Inject constructor(
    private val thirdwebRepository: ThirdwebRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(WalletUiState())
    val uiState: StateFlow<WalletUiState> = _uiState.asStateFlow()
    
    val connectionState = thirdwebRepository.connectionState
    val walletAddress = thirdwebRepository.walletAddress
    val balance = thirdwebRepository.balance
    
    init {
        initializeSDK()
    }
    
    private fun initializeSDK() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            thirdwebRepository.initializeSDK(Chain.Ethereum).fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = null
                    )
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = error.message
                    )
                }
            )
        }
    }
    
    fun connectWallet(walletType: WalletType) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            thirdwebRepository.connectWallet(walletType).fold(
                onSuccess = { address ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = null
                    )
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = error.message
                    )
                }
            )
        }
    }
    
    fun disconnectWallet() {
        viewModelScope.launch {
            thirdwebRepository.disconnectWallet().fold(
                onSuccess = {
                    _uiState.value = _uiState.value.copy(error = null)
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(error = error.message)
                }
            )
        }
    }
    
    fun refreshBalance() {
        viewModelScope.launch {
            thirdwebRepository.updateBalance()
        }
    }
    
    fun sendTransaction(to: String, amount: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            thirdwebRepository.sendTransaction(to, amount).fold(
                onSuccess = { txHash ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = null,
                        lastTransactionHash = txHash
                    )
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = error.message
                    )
                }
            )
        }
    }
    
    fun signMessage(message: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            thirdwebRepository.signMessage(message).fold(
                onSuccess = { signature ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = null,
                        lastSignature = signature
                    )
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = error.message
                    )
                }
            )
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class WalletUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val lastTransactionHash: String? = null,
    val lastSignature: String? = null
)
