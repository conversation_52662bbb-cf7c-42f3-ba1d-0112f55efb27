package com.yourapp.thirdweb.data.contract

import com.thirdweb.sdk.ThirdwebSDK
import com.thirdweb.sdk.contracts.Contract
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ContractInteractor @Inject constructor() {
    
    /**
     * Interact with ERC20 Token Contract
     */
    suspend fun getTokenBalance(
        sdk: ThirdwebSDK,
        contractAddress: String,
        walletAddress: String
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val contract = sdk.getContract(contractAddress)
            val balance = contract.erc20.balanceOf(walletAddress)
            Result.success(balance.displayValue)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Transfer ERC20 tokens
     */
    suspend fun transferTokens(
        sdk: ThirdwebSDK,
        contractAddress: String,
        to: String,
        amount: String
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val contract = sdk.getContract(contractAddress)
            val receipt = contract.erc20.transfer(to, amount)
            Result.success(receipt.transactionHash)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get NFT balance
     */
    suspend fun getNFTBalance(
        sdk: ThirdwebSDK,
        contractAddress: String,
        walletAddress: String
    ): Result<Int> = withContext(Dispatchers.IO) {
        try {
            val contract = sdk.getContract(contractAddress)
            val balance = contract.erc721.balanceOf(walletAddress)
            Result.success(balance.toInt())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Mint NFT
     */
    suspend fun mintNFT(
        sdk: ThirdwebSDK,
        contractAddress: String,
        to: String,
        metadata: NFTMetadata
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val contract = sdk.getContract(contractAddress)
            val receipt = contract.erc721.mintTo(to, metadata.toThirdwebMetadata())
            Result.success(receipt.id.toString())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Call custom contract function
     */
    suspend fun callContractFunction(
        sdk: ThirdwebSDK,
        contractAddress: String,
        functionName: String,
        args: List<Any>
    ): Result<Any> = withContext(Dispatchers.IO) {
        try {
            val contract = sdk.getContract(contractAddress)
            val result = contract.call(functionName, *args.toTypedArray())
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Read contract data
     */
    suspend fun readContract(
        sdk: ThirdwebSDK,
        contractAddress: String,
        functionName: String,
        args: List<Any> = emptyList()
    ): Result<Any> = withContext(Dispatchers.IO) {
        try {
            val contract = sdk.getContract(contractAddress)
            val result = contract.call(functionName, *args.toTypedArray())
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

data class NFTMetadata(
    val name: String,
    val description: String,
    val image: String,
    val attributes: List<NFTAttribute> = emptyList()
) {
    fun toThirdwebMetadata(): com.thirdweb.sdk.core.NFTMetadata {
        return com.thirdweb.sdk.core.NFTMetadata(
            name = name,
            description = description,
            image = image,
            attributes = attributes.map { 
                mapOf(
                    "trait_type" to it.traitType,
                    "value" to it.value
                )
            }
        )
    }
}

data class NFTAttribute(
    val traitType: String,
    val value: String
)
